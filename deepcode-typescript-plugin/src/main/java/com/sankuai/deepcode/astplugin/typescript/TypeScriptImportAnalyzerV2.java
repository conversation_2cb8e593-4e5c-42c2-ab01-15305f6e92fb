package com.sankuai.deepcode.astplugin.typescript;

import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration;
import com.intellij.lang.javascript.psi.*;
import com.intellij.lang.javascript.psi.ecma6.TypeScriptImportStatement;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.ImportInfo;
import com.sankuai.deepcode.astplugin.typescript.resolver.*;
import com.sankuai.deepcode.astplugin.typescript.resolver.VueImportResolver;
import com.sankuai.deepcode.astplugin.typescript.util.ImportUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * TypeScript/JavaScript 导入分析器 V2 - 重构版本
 * 
 * 架构设计：
 * - 单一职责：每个类只负责一个特定功能
 * - 开放封闭：易于扩展新的解析器类型
 * - 依赖倒置：依赖抽象接口而非具体实现
 * - 接口隔离：小而专一的接口
 * 
 * 核心组件：
 * - ImportResolver: 导入解析器接口
 * - PathResolver: 路径解析器
 * - FallbackStrategy: 统一兜底策略
 * - PSIResolver: PSI 解析器
 * 
 * <AUTHOR>
 */
public class TypeScriptImportAnalyzerV2 {
    
    private static final Logger LOG = Logger.getInstance(TypeScriptImportAnalyzerV2.class);
    
    private final List<ImportResolver> resolvers;
    private final FallbackStrategy fallbackStrategy;
    
    public TypeScriptImportAnalyzerV2() {
        this.resolvers = initializeResolvers();
        this.fallbackStrategy = new FallbackStrategy();
    }
    
    /**
     * 分析 TypeScript/JavaScript 文件的导入语句
     * 
     * @param jsFile JS/TS 文件
     * @param filePath 文件路径
     * @return 分析结果
     */
    public AnalysisResult analyzeImports(JSFile jsFile, String filePath) {
        try { 
            LOG.info("=== TypeScriptImportAnalyzerV2.analyzeImports() ===");
            LOG.info("Analyzing file: " + filePath);
            
            AnalysisResult result = new AnalysisResult(filePath);
            
            // 使用解析器链处理不同类型的导入
            analyzeWithResolvers(jsFile, result, filePath);
            
            LOG.info("Analysis completed. Found " + result.getImports().size() + " imports");
            return result;
            
        } catch (Exception e) {
            LOG.error("Failed to analyze imports for file: " + filePath, e);
            throw new RuntimeException("Import analysis failed", e);
        }
    }
    
    /**
     * 使用解析器链分析导入
     */
    private void analyzeWithResolvers(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            // 1. 分析 TypeScript Import 语句
            analyzeTypeScriptImports(jsFile, result, filePath);
            
            // 2. 分析 ES6 Import 声明
            analyzeES6Imports(jsFile, result, filePath);
            
            // 3. 分析 CommonJS require 调用
            analyzeRequireCalls(jsFile, result, filePath);
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze with resolvers", e);
            throw e;
        }
    }
    
    /**
     * 分析 TypeScript Import 语句
     */
    private void analyzeTypeScriptImports(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            Collection<TypeScriptImportStatement> importStatements =
                    PsiTreeUtil.findChildrenOfType(jsFile, TypeScriptImportStatement.class);
            
            LOG.debug("Found " + importStatements.size() + " TypeScript import statements");
            
            for (TypeScriptImportStatement importStatement : importStatements) {
                ImportInfo importInfo = resolveWithResolvers(importStatement, filePath);
                if (importInfo != null && !ImportUtils.isDuplicateImport(result, importInfo)) {
                    result.addImport(importInfo);
                    LOG.debug("Added TypeScript import: " + importInfo.getStatement());
                }
            }
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze TypeScript imports", e);
        }
    }
    
    /**
     * 分析 ES6 Import 声明
     */
    private void analyzeES6Imports(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            Collection<ES6ImportDeclaration> importDeclarations =
                    PsiTreeUtil.findChildrenOfType(jsFile, ES6ImportDeclaration.class);
            
            LOG.debug("Found " + importDeclarations.size() + " ES6 import declarations");
            
            for (ES6ImportDeclaration importDeclaration : importDeclarations) {
                ImportInfo importInfo = resolveWithResolvers(importDeclaration, filePath);
                if (importInfo != null && !ImportUtils.isDuplicateImport(result, importInfo)) {
                    result.addImport(importInfo);
                    LOG.debug("Added ES6 import: " + importInfo.getStatement());
                }
            }
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze ES6 imports", e);
        }
    }
    
    /**
     * 分析 CommonJS require 调用
     */
    private void analyzeRequireCalls(JSFile jsFile, AnalysisResult result, String filePath) {
        try {
            Collection<JSCallExpression> callExpressions =
                    PsiTreeUtil.findChildrenOfType(jsFile, JSCallExpression.class);
            
            int requireCount = 0;
            for (JSCallExpression callExpression : callExpressions) {
                if (isRequireCall(callExpression)) {
                    requireCount++;
                    ImportInfo importInfo = resolveWithResolvers(callExpression, filePath);
                    if (importInfo != null && !ImportUtils.isDuplicateImport(result, importInfo)) {
                        result.addImport(importInfo);
                        LOG.debug("Added require call: " + importInfo.getStatement());
                    }
                }
            }
            
            LOG.debug("Found " + requireCount + " require calls");
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze require calls", e);
        }
    }
    
    /**
     * 使用解析器链解析导入
     */
    private ImportInfo resolveWithResolvers(PsiElement importElement, String filePath) {
        try {
            for (ImportResolver resolver : resolvers) {
                if (resolver.canResolve(importElement)) {
                    LOG.debug("Using resolver: " + resolver.getResolverType() + " for element: " + 
                             importElement.getClass().getSimpleName());
                    
                    ImportInfo importInfo = resolver.resolveImport(importElement, filePath);
                    if (importInfo != null) {
                        LOG.debug("Successfully resolved with: " + resolver.getResolverType());
                        return importInfo;
                    } else {
                        LOG.debug("Resolver " + resolver.getResolverType() + " returned null");
                    }
                }
            }
            
            LOG.debug("No resolver could handle element: " + importElement.getClass().getSimpleName());
            return null;
            
        } catch (Exception e) {
            LOG.warn("Error resolving with resolvers", e);
            return null;
        }
    }
    
    /**
     * 检查是否为 require 调用
     */
    private boolean isRequireCall(JSCallExpression callExpression) {
        try {
            if (callExpression.getMethodExpression() != null) {
                String methodName = callExpression.getMethodExpression().getText();
                return "require".equals(methodName);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 初始化解析器链
     */
    private List<ImportResolver> initializeResolvers() {
        List<ImportResolver> resolverList = new ArrayList<>();

        // 按优先级添加解析器
        resolverList.add(new VueImportResolver());         // Vue import 优先级最高
        resolverList.add(new TypeScriptImportResolver());  // TypeScript import 次之
        resolverList.add(new ES6ImportResolver());         // ES6 import 再次之
        resolverList.add(new CommonJSRequireResolver());   // CommonJS require 最后

        LOG.info("Initialized " + resolverList.size() + " import resolvers");
        return resolverList;
    }
    
    /**
     * 获取支持的解析器类型
     */
    public List<String> getSupportedResolverTypes() {
        List<String> types = new ArrayList<>();
        for (ImportResolver resolver : resolvers) {
            types.add(resolver.getResolverType());
        }
        return types;
    }
    
    /**
     * 添加自定义解析器
     */
    public void addResolver(ImportResolver resolver) {
        if (resolver != null) {
            resolvers.add(resolver);
            LOG.info("Added custom resolver: " + resolver.getResolverType());
        }
    }
    
    /**
     * 移除解析器
     */
    public boolean removeResolver(String resolverType) {
        boolean removed = resolvers.removeIf(resolver -> 
            resolver.getResolverType().equals(resolverType));
        
        if (removed) {
            LOG.info("Removed resolver: " + resolverType);
        }
        
        return removed;
    }
}
