package com.sankuai.deepcode.astplugin.typescript.util;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.lang.javascript.psi.JSFunction;
import com.intellij.lang.javascript.psi.JSParameter;
import com.intellij.lang.javascript.psi.JSParameterListElement;
import com.intellij.lang.javascript.psi.ecmal4.JSClass;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.sankuai.deepcode.astplugin.model.Language;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TypeScript/JavaScript PSI 工具类
 * 提供常用的PSI访问和操作方法
 *
 * <AUTHOR>
 */
public final class TypeScriptPsiUtils {

    private static final Logger LOG = Logger.getInstance(TypeScriptPsiUtils.class);

    private TypeScriptPsiUtils() {
        // 工具类不允许实例化
    }

    /**
     * 获取文件路径（项目相对路径）
     */
    public static String getFilePath(PsiElement element) {
        try {
            PsiFile containingFile = element.getContainingFile();
            if (containingFile != null) {
                VirtualFile virtualFile = containingFile.getVirtualFile();
                if (virtualFile != null) {
                    // 获取项目根目录
                    String projectBasePath = element.getProject().getBasePath();
                    if (projectBasePath != null) {
                        String absolutePath = virtualFile.getPath();
                        // 计算相对于项目根目录的路径
                        if (absolutePath.startsWith(projectBasePath)) {
                            String relativePath = absolutePath.substring(projectBasePath.length());
                            // 移除开头的路径分隔符
                            if (relativePath.startsWith("/") || relativePath.startsWith("\\")) {
                                relativePath = relativePath.substring(1);
                            }
                            // 统一使用正斜杠作为路径分隔符
                            relativePath = relativePath.replace("\\", "/");
                            return relativePath;
                        }
                    }
                    // 如果无法获取项目根目录，返回绝对路径
                    return virtualFile.getPath();
                }
            }
            return "unknown";
        } catch (Exception e) {
            LOG.warn("Failed to get file path for element: " + element, e);
            return "unknown";
        }
    }

    /**
     * 获取模块名称（从文件名推断）
     */
    public static String getModuleName(JSFile jsFile) {
        try {
            String fileName = jsFile.getName();
            // 移除文件扩展名
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                return fileName.substring(0, lastDotIndex);
            }
            return fileName;
        } catch (Exception e) {
            LOG.warn("Failed to get module name for file: " + jsFile.getName(), e);
            return "unknown";
        }
    }

    /**
     * 获取包名/命名空间（从文件路径推断）
     */
    public static String getPackageName(PsiElement element) {
        try {
            String filePath = getFilePath(element);
            if ("unknown".equals(filePath)) {
                return "unknown";
            }

            // 简化的包名推断：使用相对路径
            VirtualFile virtualFile = element.getContainingFile().getVirtualFile();
            if (virtualFile != null && virtualFile.getParent() != null) {
                String parentPath = virtualFile.getParent().getPath();
                // 移除项目根路径，只保留相对路径
                String[] pathParts = parentPath.split("/");
                if (pathParts.length > 0) {
                    // 取最后几个路径部分作为包名
                    List<String> packageParts = Arrays.stream(pathParts)
                            .filter(part -> !part.isEmpty() && !part.equals("src") && !part.equals("main"))
                            .collect(Collectors.toList());
                    
                    if (packageParts.size() > 3) {
                        // 只取最后3个部分
                        packageParts = packageParts.subList(packageParts.size() - 3, packageParts.size());
                    }
                    
                    return String.join(".", packageParts);
                }
            }
            return "default";
        } catch (Exception e) {
            LOG.warn("Failed to get package name for element: " + element, e);
            return "default";
        }
    }

    /**
     * 获取行号
     */
    public static int getLineNumber(PsiElement element) {
        try {
            if (element == null) {
                return 0;
            }
            PsiFile containingFile = element.getContainingFile();
            if (containingFile == null) {
                return 0;
            }
            int offset = element.getTextOffset();
            String text = containingFile.getText();
            if (text == null || offset < 0 || offset >= text.length()) {
                return 0;
            }
            
            // 计算行号
            int lineNumber = 1;
            for (int i = 0; i < offset; i++) {
                if (text.charAt(i) == '\n') {
                    lineNumber++;
                }
            }
            return lineNumber;
        } catch (Exception e) {
            LOG.warn("Failed to get line number for element: " + element, e);
            return 0;
        }
    }

    /**
     * 构建函数签名
     */
    public static String buildFunctionSignature(JSFunction function) {
        try {
            if (function == null) {
                return "unknown";
            }

            StringBuilder signature = new StringBuilder();
            
            // 函数名
            String functionName = function.getName();
            if (functionName == null) {
                functionName = "anonymous";
            }
            signature.append(functionName);

            // 参数列表
            signature.append("(");
            // JSParameterListElement数组需要转换为JSParameter数组
            JSParameterListElement[] parameterElements = function.getParameters();
            JSParameter[] parameters = Arrays.stream(parameterElements)
                    .filter(element -> element instanceof JSParameter)
                    .map(element -> (JSParameter) element)
                    .toArray(JSParameter[]::new);
            for (int i = 0; i < parameters.length; i++) {
                if (i > 0) {
                    signature.append(", ");
                }
                JSParameter param = parameters[i];
                signature.append(param.getName() != null ? param.getName() : "param" + i);
                
                // 添加类型信息（如果是TypeScript）
                // 添加类型信息（如果是TypeScript）
                String typeText = getTypeText(param);
                if (typeText != null && !typeText.isEmpty()) {
                    signature.append(": ").append(typeText);
                }
            }
            signature.append(")");

            // 返回类型（如果是TypeScript）
            String returnType = getReturnTypeText(function);
            if (returnType != null && !returnType.isEmpty()) {
                signature.append(": ").append(returnType);
            }

            return signature.toString();
        } catch (Exception e) {
            LOG.warn("Failed to build function signature for: " + function, e);
            return function != null && function.getName() != null ? function.getName() : "unknown";
        }
    }

    /**
     * 获取参数类型文本
     */
    private static String getTypeText(JSParameter parameter) {
        try {
            // 在TypeScript中，参数可能有类型注解
            // 这里简化处理，实际应该解析TypeScript的类型系统
            return null; // TODO: 实现TypeScript类型解析
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取返回类型文本
     */
    private static String getReturnTypeText(JSFunction function) {
        try {
            // 在TypeScript中，函数可能有返回类型注解
            // 这里简化处理，实际应该解析TypeScript的类型系统
            return null; // TODO: 实现TypeScript返回类型解析
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 构建完整的函数ID
     */
    public static String buildFullFunctionId(JSFunction function, String className) {
        try {
            String packageName = getPackageName(function);
            String functionName = function.getName() != null ? function.getName() : "anonymous";
            
            if (className != null && !className.isEmpty()) {
                // 类方法
                return packageName + "." + className + "." + functionName;
            } else {
                // 顶层函数
                return packageName + "." + functionName;
            }
        } catch (Exception e) {
            LOG.warn("Failed to build full function ID for: " + function, e);
            return "unknown.function";
        }
    }

    /**
     * 获取类的所有方法
     */
    public static List<JSFunction> getClassMethods(JSClass jsClass) {
        try {
            return Arrays.asList(PsiTreeUtil.getChildrenOfType(jsClass, JSFunction.class));
        } catch (Exception e) {
            LOG.warn("Failed to get class methods for: " + jsClass, e);
            return List.of();
        }
    }

    /**
     * 判断语言类型（PsiFile 版本）
     */
    public static Language detectLanguage(PsiFile psiFile) {
        try {
            if (psiFile == null) {
                return Language.UNKNOWN;
            }

            String fileName = psiFile.getName();
            if (fileName.endsWith(".ts")) {
                return Language.TYPESCRIPT;
            } else if (fileName.endsWith(".tsx")) {
                return Language.TSX;
            } else if (fileName.endsWith(".jsx")) {
                return Language.JSX;
            } else if (fileName.endsWith(".js")) {
                return Language.JAVASCRIPT;
            } else if (fileName.endsWith(".vue")) {
                return Language.VUE;
            }

            // 根据语言ID判断
            String languageId = psiFile.getLanguage().getID();
            if ("TypeScript".equals(languageId) || "TypeScript JSX".equals(languageId)) {
                return fileName.endsWith(".tsx") ? Language.TSX : Language.TYPESCRIPT;
            } else if ("JavaScript".equals(languageId) || "JavaScript (JSX)".equals(languageId)) {
                return fileName.endsWith(".jsx") ? Language.JSX : Language.JAVASCRIPT;
            } else if ("Vue".equals(languageId) || "VueJS".equals(languageId)) {
                return Language.VUE;
            }

            return Language.JAVASCRIPT; // 默认
        } catch (Exception e) {
            LOG.warn("Failed to detect language for file: " + psiFile.getName(), e);
            return Language.JAVASCRIPT;
        }
    }

    /**
     * 判断语言类型（JSFile 版本）
     */
    public static Language detectLanguage(JSFile jsFile) {
        try {
            String fileName = jsFile.getName();
            if (fileName.endsWith(".ts")) {
                return Language.TYPESCRIPT;
            } else if (fileName.endsWith(".tsx")) {
                return Language.TSX;
            } else if (fileName.endsWith(".jsx")) {
                return Language.JSX;
            } else if (fileName.endsWith(".js")) {
                return Language.JAVASCRIPT;
            } else if (fileName.endsWith(".vue")) {
                return Language.VUE;
            }

            // 根据语言ID判断
            String languageId = jsFile.getLanguage().getID();
            if ("TypeScript".equals(languageId) || "TypeScript JSX".equals(languageId)) {
                return fileName.endsWith(".tsx") ? Language.TSX : Language.TYPESCRIPT;
            } else if ("JavaScript".equals(languageId) || "JavaScript (JSX)".equals(languageId)) {
                return fileName.endsWith(".jsx") ? Language.JSX : Language.JAVASCRIPT;
            } else if ("Vue".equals(languageId) || "VueJS".equals(languageId)) {
                return Language.VUE;
            }

            return Language.JAVASCRIPT; // 默认
        } catch (Exception e) {
            LOG.warn("Failed to detect language for file: " + jsFile.getName(), e);
            return Language.JAVASCRIPT;
        }
    }

    /**
     * 检查是否是React组件
     */
    public static boolean isReactComponent(JSClass jsClass) {
        try {
            if (jsClass == null) {
                return false;
            }
            
            // 检查是否继承自React.Component或其他React基类
            // 这里简化处理，实际应该检查继承关系
            JSClass[] superClasses = jsClass.getSuperClasses();
            for (JSClass superClass : superClasses) {
                String superClassName = superClass.getText();
                if (superClassName.contains("Component") || 
                    superClassName.contains("React.Component") ||
                    superClassName.contains("PureComponent")) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception e) {
            LOG.warn("Failed to check if class is React component: " + jsClass, e);
            return false;
        }
    }

    /**
     * 检查是否是函数组件
     */
    public static boolean isFunctionComponent(JSFunction function) {
        try {
            if (function == null) {
                return false;
            }
            
            // 简化的检查：函数名首字母大写，且在JSX文件中
            String functionName = function.getName();
            if (functionName != null && functionName.length() > 0 && 
                Character.isUpperCase(functionName.charAt(0))) {
                
                PsiFile containingFile = function.getContainingFile();
                if (containingFile instanceof JSFile) {
                    String fileName = containingFile.getName();
                    return fileName.endsWith(".jsx") || fileName.endsWith(".tsx");
                }
            }
            
            return false;
        } catch (Exception e) {
            LOG.warn("Failed to check if function is React component: " + function, e);
            return false;
        }
    }
}



