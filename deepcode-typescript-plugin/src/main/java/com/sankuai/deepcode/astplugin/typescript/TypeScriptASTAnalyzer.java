package com.sankuai.deepcode.astplugin.typescript;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.openapi.application.ReadAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiFile;
import com.sankuai.deepcode.astplugin.analyzer.ASTAnalyzer;
import com.sankuai.deepcode.astplugin.model.AnalysisResult;
import com.sankuai.deepcode.astplugin.model.Language;
import com.sankuai.deepcode.astplugin.typescript.util.*;
import com.sankuai.deepcode.astplugin.typescript.resolver.VueFileResolver;

/**
 * TypeScript/JavaScript语言专用的AST分析器
 * 支持 TypeScript (.ts, .tsx) 和 JavaScript (.js, .jsx) 文件分析
 *
 * <AUTHOR>
 */
public class TypeScriptASTAnalyzer implements ASTAnalyzer {

    private static final Logger LOG = Logger.getInstance(TypeScriptASTAnalyzer.class);

    @Override
    public boolean supports(PsiFile psiFile) {
        if (psiFile == null) {
            return false;
        }

        // 检查文件类型
        boolean isJSFile = psiFile instanceof JSFile;
        // 使用 JSFile 和文件扩展名来判断 TypeScript 文件
        boolean isTSFile = psiFile instanceof JSFile && 
                          (psiFile.getName().endsWith(".ts") || psiFile.getName().endsWith(".tsx"));

        // 检查文件扩展名
        String fileName = psiFile.getName();
        boolean hasCorrectExtension = fileName.endsWith(".ts") ||
                                    fileName.endsWith(".tsx") ||
                                    fileName.endsWith(".js") ||
                                    fileName.endsWith(".jsx") ||
                                    fileName.endsWith(".vue");

        // 检查语言ID
        String languageId = psiFile.getLanguage().getID();
        boolean isCorrectLanguage = "TypeScript".equals(languageId) ||
                                  "JavaScript".equals(languageId) ||
                                  "TypeScript JSX".equals(languageId) ||
                                  "JavaScript (JSX)".equals(languageId) ||
                                  "Vue".equals(languageId) ||
                                  "VueJS".equals(languageId);

        boolean supports = isJSFile || isTSFile || hasCorrectExtension || isCorrectLanguage;

        LOG.info("TypeScript analyzer supports check for file: " + fileName +
                ", isJSFile: " + isJSFile +
                ", isTSFile: " + isTSFile +
                ", hasCorrectExtension: " + hasCorrectExtension +
                ", languageId: " + languageId +
                ", isCorrectLanguage: " + isCorrectLanguage +
                ", result: " + supports);

        return supports;
    }

    @Override
    public Language getSupportedLanguage() {
        return Language.TYPESCRIPT;
    }

    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        if (!supports(psiFile)) {
            AnalysisResult result = new AnalysisResult(psiFile.getName(), "TypeScript");
            result.addError("File is not a TypeScript/JavaScript file");
            return result;
        }

        return ReadAction.compute(() -> {
            try {
                LOG.info("Starting TypeScript/JavaScript/Vue AST analysis for file: " + psiFile.getName());

                // 处理 Vue 文件
                VueFileResolver vueResolver = new VueFileResolver();
                JSFile jsFile = null;
                String fileName = psiFile.getName();

                if (vueResolver.isVueFile(psiFile)) {
                    LOG.warn("Detected Vue file, extracting script content: " + fileName);
                    jsFile = vueResolver.extractScriptFromVueFile(psiFile);
                    if (jsFile == null) {
                        LOG.warn("No JSFile found, trying to analyze Vue file directly: " + fileName);
                        // 对于 Vue 文件，即使没有 JSFile，也尝试直接分析
                        return analyzeVueFileDirectly(psiFile, fileName);
                    }
                    LOG.warn("Successfully extracted script from Vue file: " + fileName);
                } else {
                    // 处理普通的 JS/TS 文件
                    jsFile = (JSFile) psiFile;
                }

                AnalysisResult result = new AnalysisResult(fileName, getSupportedLanguage());

                // 分析文件结构（函数、类等）
                try {
                    TypeScriptStructureAnalyzer.analyzeFileStructure(jsFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze file structure: " + e.getMessage());
                    result.addError("File structure analysis failed: " + e.getMessage());
                }

                // 分析导入语句
                try {
                    // 添加调试输出
                    TypeScriptImportAnalyzer.debugImports(jsFile);
                    TypeScriptImportAnalyzer.analyzeImports(jsFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze imports: " + e.getMessage());
                    result.addError("Import analysis failed: " + e.getMessage());
                }

                // 分析模块级变量
                try {
                    TypeScriptVariableAnalyzer.analyzeModuleVariables(jsFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze module variables: " + e.getMessage());
                    result.addError("Variable analysis failed: " + e.getMessage());
                }

                // 分析调用关系
                try {
                    TypeScriptCallAnalyzer.analyzeCallRelations(jsFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to analyze call relations: " + e.getMessage());
                    result.addError("Call analysis failed: " + e.getMessage());
                }

                // 更新统计信息
                try {
                    TypeScriptStatisticsAnalyzer.updateStatistics(jsFile, result);
                } catch (Exception e) {
                    LOG.warn("Failed to update statistics: " + e.getMessage());
                    result.addError("Statistics analysis failed: " + e.getMessage());
                }

                LOG.info("TypeScript/JavaScript AST analysis completed. Nodes: " + result.getNodes().size() +
                        ", Relations: " + result.getCallRelations().size() +
                        ", Imports: " + result.getImports().size() +
                        (result.getErrors().isEmpty() ? " (no errors)" : ", Errors: " + result.getErrors().size()));

                return result;

            } catch (Exception e) {
                LOG.error("Error during TypeScript/JavaScript AST analysis", e);
                AnalysisResult errorResult = new AnalysisResult(psiFile.getName(), getSupportedLanguage());
                errorResult.addError("Analysis failed: " + e.getMessage());
                return errorResult;
            }
        });
    }

    /**
     * 直接分析 Vue 文件（不需要 JSFile）
     */
    private AnalysisResult analyzeVueFileDirectly(PsiFile vueFile, String fileName) {
        try {
            LOG.warn("Analyzing Vue file directly: " + fileName);
            AnalysisResult result = new AnalysisResult(fileName, getSupportedLanguage());

            // 暂时创建一个基本的分析结果
            result.addError("Vue file analysis is under development");
            LOG.warn("Vue file direct analysis completed for: " + fileName);
            return result;

        } catch (Exception e) {
            LOG.warn("Failed to analyze Vue file directly: " + fileName, e);
            AnalysisResult result = new AnalysisResult(fileName, getSupportedLanguage());
            result.addError("Vue file direct analysis failed: " + e.getMessage());
            return result;
        }
    }
}

