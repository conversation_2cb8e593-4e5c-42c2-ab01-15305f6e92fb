package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.psi.xml.XmlTag;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import java.util.Collection;

/**
 * Vue 文件解析器
 * 
 * 职责：
 * - 解析 Vue 文件中的 script 标签
 * - 提取 JavaScript/TypeScript 代码
 * - 利用 PSI 能力分析 Vue 组件的导入关系
 * 
 * Vue 文件结构：
 * <template>...</template>
 * <script>...</script> 或 <script lang="ts">...</script>
 * <style>...</style>
 * 
 * <AUTHOR>
 */
public class VueFileResolver {
    
    private static final Logger LOG = Logger.getInstance(VueFileResolver.class);
    
    /**
     * 从 Vue 文件中提取 JavaScript/TypeScript 代码
     *
     * @param vueFile Vue 文件的 PSI 元素
     * @return 提取的 JSFile，如果没有找到 script 标签则返回 null
     */
    public JSFile extractScriptFromVueFile(PsiFile vueFile) {
        try {
            if (vueFile == null || !isVueFile(vueFile)) {
                LOG.debug("File is null or not a Vue file");
                return null;
            }

            LOG.info("Extracting script from Vue file: " + vueFile.getName());
            LOG.info("Vue file language: " + vueFile.getLanguage().getID());
            LOG.info("Vue file class: " + vueFile.getClass().getName());

            // 调试：打印文件的所有子元素
            debugPrintFileStructure(vueFile);

            // 方法1: 查找 XmlTag 类型的 script 标签
            Collection<XmlTag> xmlTags = PsiTreeUtil.findChildrenOfType(vueFile, XmlTag.class);
            LOG.info("Found " + xmlTags.size() + " XML tags in Vue file");

            for (XmlTag tag : xmlTags) {
                LOG.info("Found XML tag: " + tag.getName());
                if ("script".equals(tag.getName())) {
                    LOG.info("Found script tag in Vue file");
                    JSFile jsFile = extractJSFileFromTag(tag);
                    if (jsFile != null) {
                        return jsFile;
                    }
                }
            }

            // 方法2: 直接查找 JSFile
            Collection<JSFile> jsFiles = PsiTreeUtil.findChildrenOfType(vueFile, JSFile.class);
            LOG.info("Found " + jsFiles.size() + " JSFile elements in Vue file");
            if (!jsFiles.isEmpty()) {
                JSFile jsFile = jsFiles.iterator().next();
                LOG.info("Using first JSFile found: " + jsFile.getName());
                return jsFile;
            }

            // 方法3: 遍历所有子元素查找
            JSFile jsFile = findJSFileInChildren(vueFile);
            if (jsFile != null) {
                LOG.info("Found JSFile in children traversal");
                return jsFile;
            }

            LOG.warn("No script content found in Vue file: " + vueFile.getName());
            return null;

        } catch (Exception e) {
            LOG.warn("Failed to extract script from Vue file: " + vueFile.getName(), e);
            return null;
        }
    }
    
    /**
     * 检查是否为 Vue 文件
     * 
     * @param psiFile PSI 文件
     * @return 是否为 Vue 文件
     */
    public boolean isVueFile(PsiFile psiFile) {
        if (psiFile == null) {
            return false;
        }
        
        String fileName = psiFile.getName();
        String languageId = psiFile.getLanguage().getID();
        
        return fileName.endsWith(".vue") || 
               "Vue".equals(languageId) || 
               "VueJS".equals(languageId);
    }
    
    /**
     * 获取 Vue 文件中 script 标签的语言类型
     * 
     * @param vueFile Vue 文件
     * @return 语言类型（"js", "ts", 或 null）
     */
    public String getScriptLanguage(PsiFile vueFile) {
        try {
            if (!isVueFile(vueFile)) {
                return null;
            }
            
            Collection<XmlTag> scriptTags = PsiTreeUtil.findChildrenOfType(vueFile, XmlTag.class);
            
            for (XmlTag tag : scriptTags) {
                if ("script".equals(tag.getName())) {
                    String lang = tag.getAttributeValue("lang");
                    if (lang != null) {
                        return lang.toLowerCase();
                    }
                    // 默认为 JavaScript
                    return "js";
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.warn("Failed to get script language from Vue file: " + vueFile.getName(), e);
            return null;
        }
    }
    
    /**
     * 检查 Vue 文件是否使用 TypeScript
     * 
     * @param vueFile Vue 文件
     * @return 是否使用 TypeScript
     */
    public boolean isTypeScriptVue(PsiFile vueFile) {
        String scriptLang = getScriptLanguage(vueFile);
        return "ts".equals(scriptLang) || "typescript".equals(scriptLang);
    }
    
    /**
     * 检查 Vue 文件是否使用 Composition API
     * 
     * @param vueFile Vue 文件
     * @return 是否使用 Composition API
     */
    public boolean isCompositionAPI(PsiFile vueFile) {
        try {
            Collection<XmlTag> scriptTags = PsiTreeUtil.findChildrenOfType(vueFile, XmlTag.class);
            
            for (XmlTag tag : scriptTags) {
                if ("script".equals(tag.getName())) {
                    String setup = tag.getAttributeValue("setup");
                    return setup != null; // 有 setup 属性表示使用 Composition API
                }
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.warn("Failed to check Composition API usage in Vue file: " + vueFile.getName(), e);
            return false;
        }
    }
    
    /**
     * 从 Vue 文件中分析导入信息
     * 
     * @param vueFile Vue 文件
     * @param filePath 文件路径
     * @return 导入信息，如果无法解析则返回 null
     */
    public ImportInfo analyzeVueImports(PsiFile vueFile, String filePath) {
        try {
            JSFile jsFile = extractScriptFromVueFile(vueFile);
            if (jsFile == null) {
                LOG.debug("No script content found in Vue file: " + filePath);
                return null;
            }
            
            // 这里可以进一步分析 JSFile 中的导入语句
            // 由于已经提取出了 JSFile，可以使用现有的 TypeScript/JavaScript 解析器
            LOG.debug("Vue file script extraction successful, can use existing JS/TS analyzers");
            
            return null; // 返回 null，让调用方使用提取出的 JSFile 进行进一步分析
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze Vue imports: " + filePath, e);
            return null;
        }
    }

    /**
     * 调试：打印文件结构
     */
    private void debugPrintFileStructure(PsiFile vueFile) {
        try {
            LOG.info("=== Vue File Structure Debug ===");
            LOG.info("File: " + vueFile.getName());
            LOG.info("File type: " + vueFile.getFileType().getName());

            PsiElement[] children = vueFile.getChildren();
            LOG.info("Direct children count: " + children.length);

            for (int i = 0; i < children.length; i++) {
                PsiElement child = children[i];
                LOG.info("Child " + i + ": " + child.getClass().getSimpleName() + " - " + child.toString().substring(0, Math.min(50, child.toString().length())));
            }

        } catch (Exception e) {
            LOG.warn("Error in debug print", e);
        }
    }

    /**
     * 从 XmlTag 中提取 JSFile
     */
    private JSFile extractJSFileFromTag(XmlTag tag) {
        try {
            LOG.info("Extracting JSFile from script tag");

            // 获取 script 标签的内容
            PsiElement[] children = tag.getChildren();
            LOG.info("Script tag has " + children.length + " children");

            for (int i = 0; i < children.length; i++) {
                PsiElement child = children[i];
                LOG.info("Script child " + i + ": " + child.getClass().getSimpleName());

                if (child instanceof JSFile) {
                    LOG.info("Found JSFile in script tag children");
                    return (JSFile) child;
                }
            }

            // 深度查找
            JSFile jsFile = PsiTreeUtil.findChildOfType(tag, JSFile.class);
            if (jsFile != null) {
                LOG.info("Found JSFile in script tag (deep search)");
                return jsFile;
            }

            // 查找文本内容并尝试解析
            String textContent = tag.getValue().getText();
            if (textContent != null && !textContent.trim().isEmpty()) {
                LOG.info("Found script text content: " + textContent.substring(0, Math.min(100, textContent.length())));
                // 这里可以尝试创建虚拟的 JSFile，但比较复杂
            }

            return null;

        } catch (Exception e) {
            LOG.warn("Error extracting JSFile from tag", e);
            return null;
        }
    }

    /**
     * 在子元素中递归查找 JSFile
     */
    private JSFile findJSFileInChildren(PsiElement element) {
        try {
            if (element instanceof JSFile) {
                return (JSFile) element;
            }

            PsiElement[] children = element.getChildren();
            for (PsiElement child : children) {
                JSFile jsFile = findJSFileInChildren(child);
                if (jsFile != null) {
                    return jsFile;
                }
            }

            return null;

        } catch (Exception e) {
            LOG.warn("Error finding JSFile in children", e);
            return null;
        }
    }
}
