package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.psi.xml.XmlTag;
import com.sankuai.deepcode.astplugin.model.ImportInfo;

import java.util.Collection;

/**
 * Vue 文件解析器
 * 
 * 职责：
 * - 解析 Vue 文件中的 script 标签
 * - 提取 JavaScript/TypeScript 代码
 * - 利用 PSI 能力分析 Vue 组件的导入关系
 * 
 * Vue 文件结构：
 * <template>...</template>
 * <script>...</script> 或 <script lang="ts">...</script>
 * <style>...</style>
 * 
 * <AUTHOR>
 */
public class VueFileResolver {
    
    private static final Logger LOG = Logger.getInstance(VueFileResolver.class);
    
    /**
     * 从 Vue 文件中提取 JavaScript/TypeScript 代码
     * 
     * @param vueFile Vue 文件的 PSI 元素
     * @return 提取的 JSFile，如果没有找到 script 标签则返回 null
     */
    public JSFile extractScriptFromVueFile(PsiFile vueFile) {
        try {
            if (vueFile == null || !isVueFile(vueFile)) {
                return null;
            }
            
            LOG.debug("Extracting script from Vue file: " + vueFile.getName());
            
            // 查找 script 标签
            Collection<XmlTag> scriptTags = PsiTreeUtil.findChildrenOfType(vueFile, XmlTag.class);
            
            for (XmlTag tag : scriptTags) {
                if ("script".equals(tag.getName())) {
                    LOG.debug("Found script tag in Vue file");
                    
                    // 获取 script 标签的内容
                    PsiElement[] children = tag.getChildren();
                    for (PsiElement child : children) {
                        if (child instanceof JSFile) {
                            LOG.debug("Successfully extracted JSFile from Vue script tag");
                            return (JSFile) child;
                        }
                    }
                    
                    // 如果直接子元素中没有 JSFile，尝试深度查找
                    JSFile jsFile = PsiTreeUtil.findChildOfType(tag, JSFile.class);
                    if (jsFile != null) {
                        LOG.debug("Successfully extracted JSFile from Vue script tag (deep search)");
                        return jsFile;
                    }
                }
            }
            
            LOG.debug("No script tag found in Vue file: " + vueFile.getName());
            return null;
            
        } catch (Exception e) {
            LOG.warn("Failed to extract script from Vue file: " + vueFile.getName(), e);
            return null;
        }
    }
    
    /**
     * 检查是否为 Vue 文件
     * 
     * @param psiFile PSI 文件
     * @return 是否为 Vue 文件
     */
    public boolean isVueFile(PsiFile psiFile) {
        if (psiFile == null) {
            return false;
        }
        
        String fileName = psiFile.getName();
        String languageId = psiFile.getLanguage().getID();
        
        return fileName.endsWith(".vue") || 
               "Vue".equals(languageId) || 
               "VueJS".equals(languageId);
    }
    
    /**
     * 获取 Vue 文件中 script 标签的语言类型
     * 
     * @param vueFile Vue 文件
     * @return 语言类型（"js", "ts", 或 null）
     */
    public String getScriptLanguage(PsiFile vueFile) {
        try {
            if (!isVueFile(vueFile)) {
                return null;
            }
            
            Collection<XmlTag> scriptTags = PsiTreeUtil.findChildrenOfType(vueFile, XmlTag.class);
            
            for (XmlTag tag : scriptTags) {
                if ("script".equals(tag.getName())) {
                    String lang = tag.getAttributeValue("lang");
                    if (lang != null) {
                        return lang.toLowerCase();
                    }
                    // 默认为 JavaScript
                    return "js";
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.warn("Failed to get script language from Vue file: " + vueFile.getName(), e);
            return null;
        }
    }
    
    /**
     * 检查 Vue 文件是否使用 TypeScript
     * 
     * @param vueFile Vue 文件
     * @return 是否使用 TypeScript
     */
    public boolean isTypeScriptVue(PsiFile vueFile) {
        String scriptLang = getScriptLanguage(vueFile);
        return "ts".equals(scriptLang) || "typescript".equals(scriptLang);
    }
    
    /**
     * 检查 Vue 文件是否使用 Composition API
     * 
     * @param vueFile Vue 文件
     * @return 是否使用 Composition API
     */
    public boolean isCompositionAPI(PsiFile vueFile) {
        try {
            Collection<XmlTag> scriptTags = PsiTreeUtil.findChildrenOfType(vueFile, XmlTag.class);
            
            for (XmlTag tag : scriptTags) {
                if ("script".equals(tag.getName())) {
                    String setup = tag.getAttributeValue("setup");
                    return setup != null; // 有 setup 属性表示使用 Composition API
                }
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.warn("Failed to check Composition API usage in Vue file: " + vueFile.getName(), e);
            return false;
        }
    }
    
    /**
     * 从 Vue 文件中分析导入信息
     * 
     * @param vueFile Vue 文件
     * @param filePath 文件路径
     * @return 导入信息，如果无法解析则返回 null
     */
    public ImportInfo analyzeVueImports(PsiFile vueFile, String filePath) {
        try {
            JSFile jsFile = extractScriptFromVueFile(vueFile);
            if (jsFile == null) {
                LOG.debug("No script content found in Vue file: " + filePath);
                return null;
            }
            
            // 这里可以进一步分析 JSFile 中的导入语句
            // 由于已经提取出了 JSFile，可以使用现有的 TypeScript/JavaScript 解析器
            LOG.debug("Vue file script extraction successful, can use existing JS/TS analyzers");
            
            return null; // 返回 null，让调用方使用提取出的 JSFile 进行进一步分析
            
        } catch (Exception e) {
            LOG.warn("Failed to analyze Vue imports: " + filePath, e);
            return null;
        }
    }
}
