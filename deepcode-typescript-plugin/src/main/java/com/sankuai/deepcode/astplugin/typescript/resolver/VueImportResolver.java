package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.lang.javascript.psi.JSFile;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;
import com.sankuai.deepcode.astplugin.model.ImportInfo;
import com.sankuai.deepcode.astplugin.typescript.util.ImportClassifier;

/**
 * Vue 文件导入解析器
 * 
 * 职责：
 * - 解析 Vue 文件中的导入语句
 * - 利用现有的 TypeScript/JavaScript 解析器处理 script 标签内容
 * - 支持 Vue 特有的导入模式
 * 
 * <AUTHOR>
 */
public class VueImportResolver implements ImportResolver {
    
    private static final Logger LOG = Logger.getInstance(VueImportResolver.class);
    
    private final VueFileResolver vueFileResolver;
    private final TypeScriptImportResolver tsResolver;
    private final ES6ImportResolver es6Resolver;
    private final CommonJSRequireResolver requireResolver;
    
    public VueImportResolver() {
        this.vueFileResolver = new VueFileResolver();
        this.tsResolver = new TypeScriptImportResolver();
        this.es6Resolver = new ES6ImportResolver();
        this.requireResolver = new CommonJSRequireResolver();
    }
    
    @Override
    public ImportInfo resolveImport(PsiElement importElement, String filePath) {
        try {
            LOG.debug("Vue resolver processing import element: " + importElement.getClass().getSimpleName());
            
            // 获取包含的文件
            PsiFile containingFile = importElement.getContainingFile();
            if (containingFile == null) {
                LOG.debug("No containing file found for import element");
                return null;
            }
            
            // 检查是否为 Vue 文件
            if (!vueFileResolver.isVueFile(containingFile)) {
                LOG.debug("Not a Vue file, skipping Vue resolver");
                return null;
            }
            
            LOG.debug("Processing Vue file import: " + containingFile.getName());
            
            // 提取 script 内容
            JSFile scriptContent = vueFileResolver.extractScriptFromVueFile(containingFile);
            if (scriptContent == null) {
                LOG.debug("No script content found in Vue file");
                return null;
            }
            
            // 使用现有的解析器处理 script 内容中的导入
            ImportInfo importInfo = resolveWithExistingResolvers(importElement, filePath);

            if (importInfo != null) {
                // ImportInfo 是不可变的，所以直接返回
                LOG.debug("Successfully resolved Vue import: " + importInfo.getStatement() +
                         " from Vue file: " + containingFile.getName());
            }

            return importInfo;
            
        } catch (Exception e) {
            LOG.warn("Failed to resolve Vue import", e);
            return null;
        }
    }
    
    @Override
    public boolean canResolve(PsiElement importElement) {
        try {
            if (importElement == null) {
                return false;
            }
            
            PsiFile containingFile = importElement.getContainingFile();
            if (containingFile == null) {
                return false;
            }
            
            // 只处理 Vue 文件中的导入
            boolean isVue = vueFileResolver.isVueFile(containingFile);
            
            if (isVue) {
                // 检查是否为支持的导入类型
                boolean canResolve = tsResolver.canResolve(importElement) ||
                                   es6Resolver.canResolve(importElement) ||
                                   requireResolver.canResolve(importElement);
                
                LOG.debug("Vue resolver can resolve: " + canResolve + 
                         " for element: " + importElement.getClass().getSimpleName() +
                         " in file: " + containingFile.getName());
                
                return canResolve;
            }
            
            return false;
            
        } catch (Exception e) {
            LOG.debug("Error checking if Vue resolver can resolve element", e);
            return false;
        }
    }
    
    @Override
    public String getResolverType() {
        return "VueImportResolver";
    }
    
    @Override
    public String resolveTargetFilePath(PsiElement importElement, String moduleSpecifier, boolean isExternal) {
        try {
            LOG.debug("Vue resolver resolving target file path for: " + moduleSpecifier);
            
            if (isExternal) {
                LOG.debug("External module, returning null for: " + moduleSpecifier);
                return null;
            }
            
            // 对于内部模块，使用现有的解析器
            String targetPath = null;
            
            if (tsResolver.canResolve(importElement)) {
                targetPath = tsResolver.resolveTargetFilePath(importElement, moduleSpecifier, isExternal);
            } else if (es6Resolver.canResolve(importElement)) {
                targetPath = es6Resolver.resolveTargetFilePath(importElement, moduleSpecifier, isExternal);
            } else if (requireResolver.canResolve(importElement)) {
                targetPath = requireResolver.resolveTargetFilePath(importElement, moduleSpecifier, isExternal);
            }
            
            if (targetPath != null) {
                LOG.debug("Vue resolver successfully resolved target path: " + targetPath);
            } else {
                LOG.debug("Vue resolver failed to resolve target path for: " + moduleSpecifier);
            }
            
            return targetPath;
            
        } catch (Exception e) {
            LOG.warn("Error resolving target file path in Vue resolver", e);
            return null;
        }
    }
    
    /**
     * 使用现有的解析器处理导入
     */
    private ImportInfo resolveWithExistingResolvers(PsiElement importElement, String filePath) {
        try {
            // 按优先级尝试不同的解析器
            if (tsResolver.canResolve(importElement)) {
                LOG.debug("Using TypeScript resolver for Vue import");
                return tsResolver.resolveImport(importElement, filePath);
            }
            
            if (es6Resolver.canResolve(importElement)) {
                LOG.debug("Using ES6 resolver for Vue import");
                return es6Resolver.resolveImport(importElement, filePath);
            }
            
            if (requireResolver.canResolve(importElement)) {
                LOG.debug("Using CommonJS resolver for Vue import");
                return requireResolver.resolveImport(importElement, filePath);
            }
            
            LOG.debug("No suitable resolver found for Vue import element");
            return null;
            
        } catch (Exception e) {
            LOG.warn("Error using existing resolvers for Vue import", e);
            return null;
        }
    }
    
    /**
     * 检查是否为 Vue 特有的导入模式
     */
    private boolean isVueSpecificImport(String moduleSpecifier) {
        if (moduleSpecifier == null) {
            return false;
        }
        
        // Vue 特有的导入模式
        return moduleSpecifier.startsWith("vue") ||
               moduleSpecifier.contains("@vue/") ||
               moduleSpecifier.endsWith(".vue") ||
               moduleSpecifier.contains("vue-router") ||
               moduleSpecifier.contains("vuex") ||
               moduleSpecifier.contains("pinia");
    }
    
    /**
     * 获取 Vue 文件的语言类型
     */
    private String getVueScriptLanguage(PsiFile vueFile) {
        return vueFileResolver.getScriptLanguage(vueFile);
    }
    
    /**
     * 检查是否为 TypeScript Vue 文件
     */
    private boolean isTypeScriptVue(PsiFile vueFile) {
        return vueFileResolver.isTypeScriptVue(vueFile);
    }
}
