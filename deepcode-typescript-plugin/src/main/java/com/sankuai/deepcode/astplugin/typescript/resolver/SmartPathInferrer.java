package com.sankuai.deepcode.astplugin.typescript.resolver;

import com.intellij.openapi.diagnostic.Logger;
import com.intellij.psi.PsiElement;
import com.intellij.psi.PsiFile;

/**
 * 智能路径推断器
 * 
 * 职责：
 * - 智能推断文件路径
 * - 处理相对路径和绝对路径
 * - 提供强制推断能力
 * 
 * <AUTHOR>
 */
public class SmartPathInferrer {
    
    private static final Logger LOG = Logger.getInstance(SmartPathInferrer.class);
    
    private final PathResolver pathResolver;
    
    public SmartPathInferrer(PathResolver pathResolver) {
        this.pathResolver = pathResolver;
    }
    
    /**
     * 智能推断文件路径
     * 
     * @param context PSI 上下文元素
     * @param moduleSpecifier 模块说明符
     * @return 推断的文件路径，失败时返回 null
     */
    public String infer(PsiElement context, String moduleSpecifier) {
        try {
            LOG.debug("Smart inferring path for: " + moduleSpecifier);
            
            if (context == null || moduleSpecifier == null) {
                return null;
            }
            
            PsiFile currentFile = context.getContainingFile();
            if (currentFile == null || currentFile.getVirtualFile() == null) {
                LOG.debug("Cannot get current file, using basic inference");
                return inferBasicPath(moduleSpecifier);
            }
            
            String currentFileAbsolutePath = currentFile.getVirtualFile().getPath();
            LOG.debug("Current file: " + currentFileAbsolutePath);
            
            if (pathResolver.isRelativePath(moduleSpecifier)) {
                return inferRelativePath(currentFileAbsolutePath, moduleSpecifier);
            } else if (isThirdPartyLibrary(moduleSpecifier)) {
                return inferThirdPartyLibraryPath(context, moduleSpecifier);
            } else {
                return inferAbsolutePath(moduleSpecifier);
            }
            
        } catch (Exception e) {
            LOG.debug("Error in smart path inference: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 推断相对路径
     * 
     * @param currentFileAbsolutePath 当前文件绝对路径
     * @param relativeModuleSpecifier 相对模块说明符
     * @return 推断的绝对路径
     */
    private String inferRelativePath(String currentFileAbsolutePath, String relativeModuleSpecifier) {
        try {
            LOG.debug("Inferring relative path: " + relativeModuleSpecifier);
            
            // 解析相对路径为绝对路径
            String targetAbsolutePath = pathResolver.resolveRelativeToAbsolute(
                currentFileAbsolutePath, relativeModuleSpecifier);
            
            if (targetAbsolutePath != null) {
                LOG.debug("Resolved relative path to: " + targetAbsolutePath);
                
                // 尝试找到实际存在的文件
                String actualFilePath = pathResolver.findActualFile(targetAbsolutePath);
                if (actualFilePath != null) {
                    LOG.debug("Found actual file: " + actualFilePath);
                    return actualFilePath;
                } else {
                    // 即使找不到实际文件，也返回推断的路径
                    String inferredPath = pathResolver.inferExtension(targetAbsolutePath);
                    LOG.debug("Inferred path with extension: " + inferredPath);
                    return inferredPath;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            LOG.debug("Error inferring relative path: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 推断绝对路径（非相对路径）
     * 
     * @param moduleSpecifier 模块说明符
     * @return 推断的路径
     */
    private String inferAbsolutePath(String moduleSpecifier) {
        try {
            LOG.debug("Inferring absolute path for: " + moduleSpecifier);
            
            // 对于别名路径
            if (moduleSpecifier.startsWith("@/")) {
                String relativePath = moduleSpecifier.substring(2);
                String result = "src/" + relativePath;
                LOG.debug("Alias path converted: " + moduleSpecifier + " -> " + result);
                return pathResolver.inferExtension(result);
            }
            
            // 对于 src/ 开头的路径
            if (moduleSpecifier.startsWith("src/")) {
                LOG.debug("Src path, using directly: " + moduleSpecifier);
                return pathResolver.inferExtension(moduleSpecifier);
            }
            
            // 对于简单模块名（没有路径分隔符）
            if (!moduleSpecifier.contains("/")) {
                String result = "src/" + moduleSpecifier;
                LOG.debug("Simple module name converted: " + moduleSpecifier + " -> " + result);
                return pathResolver.inferExtension(result);
            }
            
            // 其他情况，直接使用
            LOG.debug("Other case, using directly: " + moduleSpecifier);
            return pathResolver.inferExtension(moduleSpecifier);
            
        } catch (Exception e) {
            LOG.debug("Error inferring absolute path: " + e.getMessage());
            return pathResolver.inferExtension(moduleSpecifier);
        }
    }
    
    /**
     * 基本路径推断（当无法获取当前文件时）
     * 
     * @param moduleSpecifier 模块说明符
     * @return 推断的路径
     */
    private String inferBasicPath(String moduleSpecifier) {
        try {
            LOG.debug("Basic path inference for: " + moduleSpecifier);
            
            if (moduleSpecifier == null) {
                return "unknown.ts";
            }
            
            // 对于相对路径，直接返回（会在上层处理）
            if (pathResolver.isRelativePath(moduleSpecifier)) {
                return pathResolver.inferExtension(moduleSpecifier);
            }
            
            // 对于其他路径，使用绝对路径推断
            return inferAbsolutePath(moduleSpecifier);
            
        } catch (Exception e) {
            LOG.debug("Error in basic path inference: " + e.getMessage());
            return moduleSpecifier + ".ts";
        }
    }
    
    /**
     * 强制推断路径（兜底策略）
     * 
     * @param context PSI 上下文元素
     * @param moduleSpecifier 模块说明符
     * @return 强制推断的路径，永不返回 null
     */
    public String forceInfer(PsiElement context, String moduleSpecifier) {
        try {
            // 首先尝试正常推断
            String inferred = infer(context, moduleSpecifier);
            if (inferred != null) {
                return inferred;
            }
            
            // 兜底策略：基于模块说明符强制推断
            return forceInferByModuleSpecifier(moduleSpecifier);
            
        } catch (Exception e) {
            LOG.debug("Error in force infer: " + e.getMessage());
            return forceInferByModuleSpecifier(moduleSpecifier);
        }
    }
    
    /**
     * 基于模块说明符强制推断（最终兜底）
     * 
     * @param moduleSpecifier 模块说明符
     * @return 强制推断的路径
     */
    private String forceInferByModuleSpecifier(String moduleSpecifier) {
        if (moduleSpecifier == null) {
            return "unknown.ts";
        }
        
        try {
            // 对于相对路径，直接添加扩展名
            if (pathResolver.isRelativePath(moduleSpecifier)) {
                return pathResolver.inferExtension(moduleSpecifier);
            }
            
            // 对于别名路径
            if (moduleSpecifier.startsWith("@/")) {
                String relativePath = moduleSpecifier.substring(2);
                return pathResolver.inferExtension("src/" + relativePath);
            }
            
            // 对于 src/ 开头的路径
            if (moduleSpecifier.startsWith("src/")) {
                return pathResolver.inferExtension(moduleSpecifier);
            }
            
            // 对于简单模块名
            if (!moduleSpecifier.contains("/")) {
                return pathResolver.inferExtension("src/" + moduleSpecifier);
            }
            
            // 其他情况
            return pathResolver.inferExtension(moduleSpecifier);
            
        } catch (Exception e) {
            LOG.debug("Error in force infer by module specifier: " + e.getMessage());
            return moduleSpecifier + ".ts";
        }
    }

    /**
     * 推断第三方库路径
     *
     * @param context PSI 上下文元素
     * @param moduleSpecifier 模块说明符
     * @return 推断的第三方库路径
     */
    private String inferThirdPartyLibraryPath(PsiElement context, String moduleSpecifier) {
        try {
            LOG.debug("Inferring third-party library path for: " + moduleSpecifier);

            // 第三方库通常在 node_modules 目录中
            String nodeModulesPath = "node_modules/" + moduleSpecifier;

            // 尝试推断主文件
            String mainFile = inferThirdPartyMainFile(moduleSpecifier);
            if (mainFile != null) {
                String fullPath = nodeModulesPath + "/" + mainFile;
                LOG.debug("Inferred third-party library path: " + fullPath);
                return fullPath;
            }

            // 如果无法推断主文件，返回基本路径
            LOG.debug("Using basic third-party library path: " + nodeModulesPath);
            return nodeModulesPath;

        } catch (Exception e) {
            LOG.debug("Error inferring third-party library path: " + e.getMessage());
            return "node_modules/" + moduleSpecifier;
        }
    }

    /**
     * 推断第三方库的主文件
     *
     * @param moduleSpecifier 模块说明符
     * @return 推断的主文件路径
     */
    private String inferThirdPartyMainFile(String moduleSpecifier) {
        try {
            // 常见的主文件名
            String[] commonMainFiles = {
                "index.js", "index.ts", "index.tsx", "index.d.ts",
                "lib/index.js", "lib/index.ts", "lib/index.d.ts",
                "dist/index.js", "dist/index.ts", "dist/index.d.ts",
                "src/index.js", "src/index.ts", "src/index.tsx"
            };

            // 对于特定的库，使用已知的主文件
            if ("react".equals(moduleSpecifier)) {
                return "index.js";
            } else if ("jquery".equals(moduleSpecifier) || moduleSpecifier.startsWith("jquery.")) {
                return "dist/jquery.min.js";
            } else if (moduleSpecifier.startsWith("@")) {
                // scoped packages 通常使用 index.js
                return "index.js";
            }

            // 默认使用 index.js
            return "index.js";

        } catch (Exception e) {
            LOG.debug("Error inferring third-party main file: " + e.getMessage());
            return "index.js";
        }
    }

    /**
     * 检查是否为第三方库
     *
     * @param moduleSpecifier 模块说明符
     * @return 是否为第三方库
     */
    private boolean isThirdPartyLibrary(String moduleSpecifier) {
        if (moduleSpecifier == null || moduleSpecifier.isEmpty()) {
            return false;
        }

        // 相对路径不是第三方库
        if (pathResolver.isRelativePath(moduleSpecifier)) {
            return false;
        }

        // Node.js 内置模块不是第三方库
        if (isNodeBuiltinModule(moduleSpecifier)) {
            return false;
        }

        // 其他情况都认为是第三方库
        return true;
    }

    /**
     * 检查是否为 Node.js 内置模块
     *
     * @param moduleSpecifier 模块说明符
     * @return 是否为 Node.js 内置模块
     */
    private boolean isNodeBuiltinModule(String moduleSpecifier) {
        String[] builtinModules = {
            "assert", "buffer", "child_process", "cluster", "crypto", "dgram", "dns",
            "events", "fs", "http", "https", "net", "os", "path", "querystring",
            "readline", "stream", "string_decoder", "timers", "tls", "tty", "url",
            "util", "v8", "vm", "zlib", "constants", "domain", "punycode"
        };

        for (String builtin : builtinModules) {
            if (builtin.equals(moduleSpecifier)) {
                return true;
            }
        }

        return false;
    }
}
