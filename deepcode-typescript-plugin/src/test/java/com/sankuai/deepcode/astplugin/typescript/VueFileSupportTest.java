package com.sankuai.deepcode.astplugin.typescript;

import com.sankuai.deepcode.astplugin.model.Language;
import com.sankuai.deepcode.astplugin.typescript.resolver.VueFileResolver;

/**
 * Vue 文件支持测试
 *
 * 测试内容：
 * - Vue 文件检测
 * - Language 枚举支持
 * - Vue 文件解析器功能
 * - TypeScriptASTAnalyzer 对 Vue 文件的支持
 *
 * <AUTHOR>
 */
public class VueFileSupportTest {

    private VueFileResolver vueResolver;
    private TypeScriptASTAnalyzer analyzer;

    public VueFileSupportTest() {
        vueResolver = new VueFileResolver();
        analyzer = new TypeScriptASTAnalyzer();
    }

    /**
     * 运行所有测试
     */
    public static void main(String[] args) {
        VueFileSupportTest test = new VueFileSupportTest();
        test.runAllTests();
    }

    public void runAllTests() {
        System.out.println("=== Vue File Support Tests ===");

        testLanguageEnumSupportsVue();
        testLanguageFromFileExtension();
        testLanguageFromFileName();
        testVueFileResolverBasicFunctionality();
        testTypeScriptAnalyzerSupportsVueFiles();
        testVueFileExtensionHandling();
        testVueImportPatterns();
        testVueFilePathResolution();
        testVueFileAnalysisWorkflow();

        System.out.println("=== All Vue tests completed ===");
    }
    
    @Test
    void testLanguageEnumSupportsVue() {
        // 测试 Language 枚举是否支持 Vue
        Language vueLanguage = Language.VUE;
        assertNotNull(vueLanguage, "Vue language should be defined");
        assertEquals("vue", vueLanguage.getCode(), "Vue language code should be 'vue'");
        assertEquals("Vue", vueLanguage.getDisplayName(), "Vue display name should be 'Vue'");
        assertEquals(".vue", vueLanguage.getFileExtension(), "Vue file extension should be '.vue'");
    }
    
    @Test
    void testLanguageFromFileExtension() {
        // 测试从文件扩展名获取语言类型
        Language language = Language.fromFileExtension(".vue");
        assertEquals(Language.VUE, language, "Should detect Vue language from .vue extension");
        
        language = Language.fromFileExtension("vue");
        assertEquals(Language.VUE, language, "Should detect Vue language from vue extension");
    }
    
    @Test
    void testLanguageFromFileName() {
        // 测试从文件名获取语言类型
        Language language = Language.fromFileName("App.vue");
        assertEquals(Language.VUE, language, "Should detect Vue language from App.vue filename");
        
        language = Language.fromFileName("components/HelloWorld.vue");
        assertEquals(Language.VUE, language, "Should detect Vue language from path with .vue extension");
        
        language = Language.fromFileName("test.vue.backup");
        assertEquals(Language.UNKNOWN, language, "Should not detect Vue for files ending with .vue but having other extensions");
    }
    
    @Test
    void testVueFileResolverBasicFunctionality() {
        // 测试 VueFileResolver 基本功能
        assertNotNull(vueResolver, "VueFileResolver should be instantiated");
        
        // 测试 Vue 文件检测逻辑（模拟）
        // 注意：这里无法创建真实的 PsiFile，所以只测试基本逻辑
        String vueScriptLang = vueResolver.getScriptLanguage(null);
        assertNull(vueScriptLang, "Should return null for null input");
    }
    
    @Test
    void testTypeScriptAnalyzerSupportsVueFiles() {
        // 测试 TypeScriptASTAnalyzer 是否声明支持 Vue 文件
        assertNotNull(analyzer, "TypeScriptASTAnalyzer should be instantiated");
        
        // 测试支持的语言
        Language supportedLanguage = analyzer.getSupportedLanguage();
        assertEquals(Language.TYPESCRIPT, supportedLanguage, "Analyzer should support TypeScript language");
        
        // 注意：由于 supports() 方法需要 PsiFile 参数，这里无法直接测试
        // 在实际环境中需要创建模拟的 Vue PsiFile 来测试
    }
    
    @Test
    void testVueFileExtensionHandling() {
        // 测试各种 Vue 文件扩展名处理
        String[] vueFiles = {
            "App.vue",
            "components/HelloWorld.vue", 
            "views/Home.vue",
            "layouts/Default.vue"
        };
        
        for (String fileName : vueFiles) {
            Language detected = Language.fromFileName(fileName);
            assertEquals(Language.VUE, detected, 
                "Should detect Vue language for file: " + fileName);
        }
    }
    
    @Test
    void testVueImportPatterns() {
        // 测试 Vue 相关的导入模式识别
        String[] vueImports = {
            "vue",
            "@vue/composition-api",
            "vue-router",
            "vuex",
            "pinia",
            "./components/HelloWorld.vue",
            "@/components/MyComponent.vue"
        };
        
        // 这里可以测试导入模式的识别逻辑
        for (String importPath : vueImports) {
            boolean isVueRelated = isVueRelatedImport(importPath);
            assertTrue(isVueRelated, "Should recognize Vue-related import: " + importPath);
        }
    }
    
    @Test
    void testVueFileScriptLanguageDetection() {
        // 测试 Vue 文件中 script 标签语言检测
        // 模拟不同的 script 标签配置
        String[] scriptConfigs = {
            "js",      // <script>
            "ts",      // <script lang="ts">
            "typescript" // <script lang="typescript">
        };
        
        for (String config : scriptConfigs) {
            // 在实际实现中，这里会测试 VueFileResolver.getScriptLanguage()
            // 目前只是验证测试结构
            assertNotNull(config, "Script config should not be null");
        }
    }
    
    @Test
    void testVueCompositionAPIDetection() {
        // 测试 Vue Composition API 检测
        // 模拟 <script setup> 标签
        boolean hasSetup = true; // 模拟检测结果
        assertTrue(hasSetup, "Should detect Composition API usage");
    }
    
    /**
     * 辅助方法：检查是否为 Vue 相关的导入
     */
    private boolean isVueRelatedImport(String moduleSpecifier) {
        if (moduleSpecifier == null) {
            return false;
        }
        
        return moduleSpecifier.startsWith("vue") ||
               moduleSpecifier.contains("@vue/") ||
               moduleSpecifier.endsWith(".vue") ||
               moduleSpecifier.contains("vue-router") ||
               moduleSpecifier.contains("vuex") ||
               moduleSpecifier.contains("pinia");
    }
    
    @Test
    void testVueFilePathResolution() {
        // 测试 Vue 文件路径解析
        String[] testPaths = {
            "./App.vue",
            "../components/HelloWorld.vue",
            "@/views/Home.vue",
            "~/components/MyComponent.vue"
        };
        
        for (String path : testPaths) {
            boolean isValidVuePath = path.endsWith(".vue") || path.contains(".vue");
            assertTrue(isValidVuePath, "Should recognize Vue file path: " + path);
        }
    }
    
    @Test
    void testVueFileAnalysisWorkflow() {
        // 测试 Vue 文件分析工作流程
        // 1. 检测 Vue 文件
        // 2. 提取 script 内容
        // 3. 分析导入语句
        // 4. 分析调用关系
        
        String vueFileName = "TestComponent.vue";
        Language detectedLanguage = Language.fromFileName(vueFileName);
        assertEquals(Language.VUE, detectedLanguage, "Should detect Vue language");
        
        // 模拟分析流程
        boolean analysisSuccessful = true; // 在实际实现中会进行真实分析
        assertTrue(analysisSuccessful, "Vue file analysis should be successful");
    }
}
