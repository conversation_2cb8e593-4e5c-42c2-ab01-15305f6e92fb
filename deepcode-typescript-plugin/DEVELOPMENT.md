# TypeScript 插件开发指南

本文档介绍 DeepCode TypeScript Plugin 的开发环境搭建、架构设计、核心实现和扩展方法。

## 🏗️ 架构设计

### 核心组件

```
deepcode-typescript-plugin/src/main/java/
└── com/sankuai/deepcode/astplugin/typescript/
    ├── TypeScriptASTAnalyzer.java      # 主分析器
    ├── TypeScriptNodeVisitor.java      # TypeScript PSI 访问器
    ├── TypeScriptCallAnalyzer.java     # 调用关系分析
    ├── TypeScriptImportAnalyzer.java   # 导入/导出分析
    ├── TypeScriptTypeAnalyzer.java     # 类型系统分析
    └── util/
        ├── TypeScriptPsiUtils.java     # TypeScript PSI 工具
        ├── TypeScriptSignatureUtils.java # 签名和类型工具
        ├── TypeScriptModuleDetector.java # 模块和包检测
        └── TypeScriptDecoratorUtils.java # 装饰器工具
```

### 分析流程

```
PsiFile (TypeScript/JavaScript)
    ↓
TypeScriptASTAnalyzer
    ├── TypeScriptNodeVisitor (遍历 TypeScript PSI)
    ├── TypeScriptCallAnalyzer (分析调用关系)
    ├── TypeScriptImportAnalyzer (分析导入导出)
    ├── TypeScriptTypeAnalyzer (分析类型系统)
    └── TypeScriptModuleDetector (检测模块结构)
    ↓
AnalysisResult (统一结果格式)
```

### TypeScript PSI 层次结构

```
JSFile (JavaScript/TypeScript 文件)
├── TypeScriptInterface (接口定义)
├── TypeScriptClass (类定义)
├── TypeScriptFunction (函数定义)
├── TypeScriptTypeAlias (类型别名)
├── TypeScriptEnum (枚举定义)
├── ES6ImportDeclaration (导入语句)
├── ES6ExportDeclaration (导出语句)
└── JSCallExpression (调用表达式)
```

## 🛠️ 开发环境

### 环境要求

- **Java 17+** (开发语言)
- **WebStorm 2024.1.4+** 或 **IntelliJ IDEA + JavaScript 插件** (开发环境)
- **Gradle 8.10+** (构建工具)
- **Node.js 16+** (用于测试)
- **TypeScript 4.0+** (用于测试代码)

### 项目配置

**build.gradle.kts**:
```kotlin
plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.17.4"
}

dependencies {
    implementation(project(":shared-core"))
    implementation(project(":shared-ui"))

    // TypeScript 测试依赖
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.mockito:mockito-core:4.11.0")
}

intellij {
    version.set("2024.1.4")
    type.set("WS")  // WebStorm
    plugins.set(listOf("JavaScript"))
}

tasks.withType<Test> {
    systemProperty("idea.test.execution.policy", "com.intellij.execution.junit.JUnitTestExecutionPolicy")
}
```

### 开发启动

```bash
# 克隆项目
git clone <repository>
cd deepcode-ast-plugin

# 确保 Node.js 环境
node --version  # 确保 16+ 版本
npm --version

# 初始化测试环境
cd deepcode-typescript-plugin/src/test/resources/
npm init -y
npm install typescript @types/node

# 构建项目
./gradlew :deepcode-typescript-plugin:build

# 启动开发环境 (WebStorm)
./gradlew :deepcode-typescript-plugin:runIde

# 调试模式
./gradlew :deepcode-typescript-plugin:runIde --debug-jvm
```

### 测试项目设置

在测试资源目录创建示例 TypeScript 项目：

**src/test/resources/typescript-test/tsconfig.json**:
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "esModuleInterop": true,
    "skipLibCheck": true
  }
}
```

**src/test/resources/typescript-test/sample.ts**:
```typescript
export interface IUser {
  id: number;
  name: string;
}

export class UserService {
  constructor(private repository: IUserRepository) {}

  async getUser(id: number): Promise<IUser | null> {
    return this.repository.findById(id);
  }
}
```

## 🔍 核心实现

### TypeScriptASTAnalyzer

主分析器负责协调整个分析过程：

```java
public class TypeScriptASTAnalyzer implements ASTAnalyzer {

    @Override
    public boolean supports(PsiFile psiFile) {
        return psiFile instanceof JSFile && isTypeScriptFile(psiFile);
    }

    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        return ReadAction.compute(() -> {
            AnalysisResult result = new AnalysisResult();
            result.setFilePath(psiFile.getVirtualFile().getPath());
            result.setLanguage(Language.TYPESCRIPT);

            JSFile jsFile = (JSFile) psiFile;

            // 1. 检测模块结构
            String moduleName = TypeScriptModuleDetector.detectModule(jsFile);

            // 2. 分析文件结构
            TypeScriptNodeVisitor visitor = new TypeScriptNodeVisitor(result, moduleName);
            jsFile.accept(visitor);

            // 3. 分析调用关系
            TypeScriptCallAnalyzer callAnalyzer = new TypeScriptCallAnalyzer(result);
            callAnalyzer.analyzeCallRelations(jsFile);

            // 4. 分析导入导出关系
            TypeScriptImportAnalyzer importAnalyzer = new TypeScriptImportAnalyzer(result);
            importAnalyzer.analyzeImports(jsFile);

            // 5. 分析类型系统
            TypeScriptTypeAnalyzer typeAnalyzer = new TypeScriptTypeAnalyzer(result);
            typeAnalyzer.analyzeTypes(jsFile);

            return result;
        });
    }

    private boolean isTypeScriptFile(PsiFile psiFile) {
        String fileName = psiFile.getName();
        return fileName.endsWith(".ts") || fileName.endsWith(".tsx");
    }
}
```

### TypeScriptNodeVisitor

TypeScript PSI 访问器，处理各种节点类型：

```java
public class TypeScriptNodeVisitor extends JSElementVisitor {
    private final AnalysisResult result;
    private final String moduleName;

    public TypeScriptNodeVisitor(AnalysisResult result, String moduleName) {
        this.result = result;
        this.moduleName = moduleName;
    }

    @Override
    public void visitTypeScriptInterface(TypeScriptInterface tsInterface) {
        AnalysisNode interfaceNode = createInterfaceNode(tsInterface);
        result.addNode(interfaceNode);
        super.visitTypeScriptInterface(tsInterface);
    }

    @Override
    public void visitTypeScriptClass(TypeScriptClass tsClass) {
        AnalysisNode classNode = createClassNode(tsClass);
        result.addNode(classNode);
        super.visitTypeScriptClass(tsClass);
    }

    @Override
    public void visitTypeScriptFunction(TypeScriptFunction tsFunction) {
        AnalysisNode functionNode = createFunctionNode(tsFunction);
        result.addNode(functionNode);
        super.visitTypeScriptFunction(tsFunction);
    }

    @Override
    public void visitTypeScriptTypeAlias(TypeScriptTypeAlias typeAlias) {
        AnalysisNode typeNode = createTypeAliasNode(typeAlias);
        result.addNode(typeNode);
        super.visitTypeScriptTypeAlias(typeAlias);
    }

    // 创建接口节点
    private AnalysisNode createInterfaceNode(TypeScriptInterface tsInterface) {
        AnalysisNode node = new AnalysisNode();
        node.setId(TypeScriptPsiUtils.getFullyQualifiedName(tsInterface));
        node.setType(NodeType.INTERFACE);
        node.setName(tsInterface.getName());
        node.setPackageName(moduleName);
        node.setLineNumber(TypeScriptPsiUtils.getLineNumber(tsInterface));

        // 处理泛型参数
        List<String> typeParameters = TypeScriptPsiUtils.getTypeParameters(tsInterface);
        if (!typeParameters.isEmpty()) {
            node.addMetadata("typeParameters", typeParameters);
        }

        // 检查是否导出
        boolean isExported = TypeScriptPsiUtils.isExported(tsInterface);
        node.addMetadata("exported", isExported);

        node.setSignature(TypeScriptSignatureUtils.getInterfaceSignature(tsInterface));
        node.setLanguage(Language.TYPESCRIPT);

        return node;
    }
}
```

### TypeScriptCallAnalyzer

调用关系分析器：

```java
public class TypeScriptCallAnalyzer {
    private final AnalysisResult result;

    public TypeScriptCallAnalyzer(AnalysisResult result) {
        this.result = result;
    }

    public void analyzeCallRelations(JSFile jsFile) {
        CallExpressionVisitor visitor = new CallExpressionVisitor();
        jsFile.accept(visitor);
    }

    private class CallExpressionVisitor extends JSElementVisitor {

        @Override
        public void visitJSCallExpression(JSCallExpression callExpression) {
            analyzeCall(callExpression);
            super.visitJSCallExpression(callExpression);
        }

        private void analyzeCall(JSCallExpression callExpression) {
            String callerId = findCallerId(callExpression);
            String calleeId = extractCalleeId(callExpression);

            if (callerId != null && calleeId != null) {
                boolean isInternal = isInternalCall(calleeId);
                int lineNumber = TypeScriptPsiUtils.getLineNumber(callExpression);
                String context = TypeScriptPsiUtils.getCallContext(callExpression);

                CallRelation callRelation = new CallRelation();
                callRelation.setCallerId(callerId);
                callRelation.setCalleeId(calleeId);
                callRelation.setInternal(isInternal);
                callRelation.addInstance(lineNumber, context);

                result.addCallRelation(callRelation);
            }
        }

        private boolean isInternalCall(String calleeId) {
            // 检查是否为内置 JavaScript/浏览器 API
            if (isBuiltInAPI(calleeId)) return false;

            // 检查是否为第三方库
            if (isThirdPartyLibrary(calleeId)) return false;

            // 检查是否在当前项目中定义
            return result.getNodes().containsKey(calleeId);
        }

        private boolean isBuiltInAPI(String calleeId) {
            return BUILTIN_APIS.contains(calleeId) ||
                   calleeId.startsWith("console.") ||
                   calleeId.startsWith("JSON.") ||
                   calleeId.startsWith("document.");
        }
    }

    private static final Set<String> BUILTIN_APIS = Set.of(
        "console.log", "JSON.parse", "Array.from",
        "Promise.resolve", "fetch", "setTimeout"
    );
}
```

## 🔧 工具类实现

### TypeScriptPsiUtils

提供 TypeScript PSI 操作的工具方法：

```java
public class TypeScriptPsiUtils {

    public static String getFullyQualifiedName(PsiElement element) {
        if (element instanceof TypeScriptInterface) {
            TypeScriptInterface tsInterface = (TypeScriptInterface) element;
            return getModuleName(tsInterface) + "." + tsInterface.getName();
        }
        if (element instanceof TypeScriptClass) {
            TypeScriptClass tsClass = (TypeScriptClass) element;
            return getModuleName(tsClass) + "." + tsClass.getName();
        }
        return element.getText();
    }

    public static String getModuleName(PsiElement element) {
        PsiFile containingFile = element.getContainingFile();
        if (containingFile == null) return "unknown";

        String fileName = containingFile.getName();
        int dotIndex = fileName.lastIndexOf('.');
        return dotIndex > 0 ? fileName.substring(0, dotIndex) : fileName;
    }

    public static int getLineNumber(PsiElement element) {
        Document document = PsiDocumentManager.getInstance(element.getProject())
                                              .getDocument(element.getContainingFile());
        if (document == null) return -1;

        return document.getLineNumber(element.getTextOffset()) + 1;
    }

    public static boolean isExported(PsiElement element) {
        PsiElement parent = element.getParent();
        while (parent != null) {
            if (parent instanceof ES6ExportDeclaration) {
                return true;
            }
            if (parent.getText().startsWith("export ")) {
                return true;
            }
            parent = parent.getParent();
        }
        return false;
    }

    public static String getCallContext(JSCallExpression callExpression) {
        String context = callExpression.getText();
        if (context.length() > 100) {
            context = context.substring(0, 50) + "..." +
                     context.substring(context.length() - 47);
        }
        return context;
    }
}
```

## 🧪 测试开发

### 单元测试框架

```java
public class TypeScriptASTAnalyzerTest extends BasePlatformTestCase {

    private TypeScriptASTAnalyzer analyzer;

    @Override
    protected void setUp() throws Exception {
        super.setUp();
        analyzer = new TypeScriptASTAnalyzer();
    }

    @Test
    public void testInterfaceAnalysis() {
        String tsCode = """
            export interface IUser {
                id: number;
                name: string;
                email: string;
            }
            """;

        JSFile tsFile = createTypeScriptFile("test.ts", tsCode);
        AnalysisResult result = analyzer.analyze(tsFile);

        assertEquals(1, result.getNodes().size());
        AnalysisNode node = result.getNodes().values().iterator().next();
        assertEquals(NodeType.INTERFACE, node.getType());
        assertEquals("IUser", node.getName());
        assertTrue((Boolean) node.getMetadata().get("exported"));
    }

    @Test
    public void testClassAnalysis() {
        String tsCode = """
            @Injectable()
            export class UserService {
                constructor(private repository: IUserRepository) {}

                async getUser(id: number): Promise<IUser | null> {
                    return this.repository.findById(id);
                }
            }
            """;

        JSFile tsFile = createTypeScriptFile("service.ts", tsCode);
        AnalysisResult result = analyzer.analyze(tsFile);

        // 验证类节点
        AnalysisNode classNode = result.getNodes().get("service.UserService");
        assertNotNull(classNode);
        assertEquals(NodeType.CLASS, classNode.getType());

        List<String> decorators = (List<String>) classNode.getMetadata().get("decorators");
        assertNotNull(decorators);
        assertTrue(decorators.contains("@Injectable"));
    }

    private JSFile createTypeScriptFile(String fileName, String content) {
        return (JSFile) myFixture.configureByText(fileName, content);
    }
}
```

### 调试技巧

#### 启用调试模式

```bash
# 方式一：环境变量
export AST_ANALYZER_DEBUG=true
./gradlew :deepcode-typescript-plugin:runIde

# 方式二：JVM 参数
./gradlew :deepcode-typescript-plugin:runIde -Dast.analyzer.debug=true
```

#### 查看调试输出

```java
public class TypeScriptAnalyzerLogger {
    private static final Logger LOG = Logger.getInstance(TypeScriptAnalyzerLogger.class);
    private static boolean debugEnabled = Boolean.getBoolean("ast.analyzer.debug");

    public static void debug(String message, Object... args) {
        if (debugEnabled) {
            LOG.info("[TS-DEBUG] " + String.format(message, args));
        }
    }

    public static void logNodeCreation(AnalysisNode node) {
        debug("Created node: %s (%s) at line %d",
              node.getName(), node.getType(), node.getLineNumber());
    }
}
```

## 🔄 扩展和优化

### 添加新语言特性

```java
// 扩展节点访问器支持新特性
public class EnhancedTypeScriptNodeVisitor extends TypeScriptNodeVisitor {

    @Override
    public void visitTypeScriptMappedType(TypeScriptMappedType mappedType) {
        // 处理映射类型：type Readonly<T> = { readonly [P in keyof T]: T[P] }
        AnalysisNode node = createMappedTypeNode(mappedType);
        result.addNode(node);
        super.visitTypeScriptMappedType(mappedType);
    }

    @Override
    public void visitTypeScriptConditionalType(TypeScriptConditionalType conditionalType) {
        // 处理条件类型：type NonNullable<T> = T extends null | undefined ? never : T
        AnalysisNode node = createConditionalTypeNode(conditionalType);
        result.addNode(node);
        super.visitTypeScriptConditionalType(conditionalType);
    }
}
```

### 性能优化

```java
public class OptimizedTypeScriptAnalyzer extends TypeScriptASTAnalyzer {

    private final Map<String, AnalysisResult> cache = new ConcurrentHashMap<>();

    @Override
    public AnalysisResult analyze(PsiFile psiFile) {
        // 缓存策略
        String cacheKey = generateCacheKey(psiFile);
        if (cache.containsKey(cacheKey)) {
            return cache.get(cacheKey);
        }

        AnalysisResult result = super.analyze(psiFile);
        cache.put(cacheKey, result);
        return result;
    }

    // 批处理优化
    public List<AnalysisResult> analyzeMultipleFiles(List<PsiFile> files) {
        return files.parallelStream()
                   .filter(this::supports)
                   .map(this::analyze)
                   .collect(Collectors.toList());
    }
}
```

## 🏗️ 构建和发布

### Gradle 配置

```kotlin
// build.gradle.kts
tasks {
    patchPluginXml {
        changeNotes.set("""
            <h3>1.0.0</h3>
            <ul>
                <li>Initial release of TypeScript AST Analyzer</li>
                <li>Support for interfaces, classes, functions, and type aliases</li>
                <li>Call relation analysis with internal/external distinction</li>
                <li>Decorator and type annotation support</li>
                <li>Export capabilities (JSON, CSV, HTML)</li>
            </ul>
        """)
    }

    buildPlugin {
        archiveFileName.set("deepcode-typescript-plugin-${project.version}.zip")
    }
}
```

### 自动化测试

```bash
#!/bin/bash
# scripts/run-tests.sh

echo "🧪 Running TypeScript Plugin Tests..."

# 单元测试
./gradlew :deepcode-typescript-plugin:test

# 集成测试
./gradlew :deepcode-typescript-plugin:integrationTest

# 构建插件
./gradlew :deepcode-typescript-plugin:buildPlugin

# 验证插件
./gradlew :deepcode-typescript-plugin:verifyPlugin

echo "✅ All tests completed!"
```

## 📝 开发最佳实践

### PSI 访问规范

1. **所有 PSI 访问必须在 ReadAction 中进行**：
```java
// ✅ 正确
ReadAction.run(() -> {
    String className = psiClass.getName();
    processClass(className);
});

// ❌ 错误
String className = psiClass.getName(); // 可能导致 UI 线程阻塞
```

2. **批量 PSI 操作优化**：
```java
// ✅ 正确：一次性获取所有信息
ReadAction.run(() -> {
    String name = element.getName();
    int line = getLineNumber(element);
    String signature = getSignature(element);
    // 处理所有信息
});

// ❌ 错误：多次进入 ReadAction
String name = ReadAction.compute(() -> element.getName());
int line = ReadAction.compute(() -> getLineNumber(element));
```

### 错误处理

```java
public AnalysisResult analyze(PsiFile psiFile) {
    try {
        return ReadAction.compute(() -> {
            // 分析逻辑
            return performAnalysis(psiFile);
        });
    } catch (Exception e) {
        LOG.error("Failed to analyze TypeScript file: " + psiFile.getName(), e);
        return createEmptyResult();
    }
}
```

### 内存管理

```java
public class TypeScriptAnalyzerMemoryManager {

    private static final int MAX_CACHE_SIZE = 100;
    private final LinkedHashMap<String, AnalysisResult> cache =
        new LinkedHashMap<String, AnalysisResult>(MAX_CACHE_SIZE, 0.75f, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<String, AnalysisResult> eldest) {
                return size() > MAX_CACHE_SIZE;
            }
        };

    public void clearCache() {
        cache.clear();
    }
}
```

## 🔗 相关文档

- [TypeScript 插件使用指南](USAGE.md)
- [TypeScript 插件简介](README.md)
- [项目总体架构](../README.md)
- [共享核心模块](../shared-core/README.md)
- [共享UI模块](../shared-ui/README.md)
- [Java插件开发指南](../deepcode-java-plugin/DEVELOPMENT.md)
- [Python插件开发指南](../deepcode-python-plugin/DEVELOPMENT.md)

## 🐛 故障排除

### 常见问题

1. **插件未启动**：检查 WebStorm 或 IDEA 的 JavaScript 插件是否启用
2. **PSI 解析错误**：确认 TypeScript 代码语法正确
3. **类型推断失败**：检查 tsconfig.json 配置
4. **内存不足**：增加 IDE 内存配置

### 性能监控

```java
public class TypeScriptAnalyzerProfiler {

    public static void profileAnalysis(Supplier<AnalysisResult> analysisTask) {
        long startTime = System.currentTimeMillis();
        AnalysisResult result = analysisTask.get();
        long endTime = System.currentTimeMillis();

        LOG.info("Analysis completed in {}ms, found {} nodes",
                endTime - startTime, result.getNodes().size());
    }
}
```

通过遵循这个开发指南，开发者可以有效地扩展和维护 TypeScript 插件，处理复杂的 TypeScript 语言特性，并保持良好的代码质量和性能表现。

