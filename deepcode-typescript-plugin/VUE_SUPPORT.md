# Vue 文件支持功能说明

## 概述

TypeScript 插件现已支持 Vue.js 单文件组件（.vue 文件）的解析和分析。该功能利用 PSI（Program Structure Interface）能力，能够正确解析 Vue 文件中的 JavaScript/TypeScript 代码，分析导入关系和调用关系。

## 已实现的功能

### 1. 语言支持扩展

**文件**: `shared-core/src/main/java/com/sankuai/deepcode/astplugin/model/Language.java`

- 添加了 `VUE("vue", "Vue", ".vue")` 枚举值
- 支持通过文件扩展名 `.vue` 自动识别 Vue 文件
- 支持通过文件名自动检测 Vue 语言类型

### 2. TypeScript 分析器扩展

**文件**: `deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/TypeScriptASTAnalyzer.java`

- 扩展 `supports()` 方法，添加对 `.vue` 文件扩展名的检测
- 添加对 Vue 语言 ID（"Vue", "VueJS"）的识别
- 修改 `analyze()` 方法，支持从 Vue 文件中提取 script 标签内容进行分析

### 3. Vue 文件解析器

**文件**: `deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/resolver/VueFileResolver.java`

**核心功能**:
- `extractScriptFromVueFile()`: 从 Vue 文件中提取 `<script>` 标签内容
- `isVueFile()`: 检测文件是否为 Vue 文件
- `getScriptLanguage()`: 获取 script 标签的语言类型（js/ts）
- `isTypeScriptVue()`: 检测是否为 TypeScript Vue 文件
- `isCompositionAPI()`: 检测是否使用 Composition API（`<script setup>`）

**支持的 Vue 文件结构**:
```vue
<template>
  <!-- Vue 模板 -->
</template>

<script>
// JavaScript 代码
</script>

<script lang="ts">
// TypeScript 代码
</script>

<script setup>
// Composition API
</script>

<style>
/* CSS 样式 */
</style>
```

### 4. Vue 导入解析器

**文件**: `deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/resolver/VueImportResolver.java`

**功能特性**:
- 专门处理 Vue 文件中的导入语句
- 利用现有的 TypeScript/ES6/CommonJS 解析器处理 script 内容
- 支持 Vue 特有的导入模式识别
- 集成到解析器链中，优先级最高

**支持的导入模式**:
- Vue 框架导入: `import { ref } from 'vue'`
- Vue 组件导入: `import MyComponent from './MyComponent.vue'`
- Vue 生态导入: `vue-router`, `vuex`, `pinia`, `@vue/*`

### 5. 解析器链集成

**文件**: `deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/TypeScriptImportAnalyzerV2.java`

- 将 `VueImportResolver` 添加到解析器链的最高优先级
- 确保 Vue 文件的导入语句能够被正确处理

### 6. 工具类扩展

**文件**: `deepcode-typescript-plugin/src/main/java/com/sankuai/deepcode/astplugin/typescript/util/TypeScriptPsiUtils.java`

- 扩展 `detectLanguage()` 方法，添加对 Vue 文件的语言检测
- 支持通过文件扩展名和语言 ID 识别 Vue 文件

### 7. 文件扩展名支持

现有的文件解析器已经包含对 `.vue` 扩展名的支持：
- `FileExtensionInferrer.java`: 扩展名推断
- `FileSystemResolver.java`: 文件系统解析
- 各种导入解析工具类

## 技术实现细节

### PSI 解析策略

1. **文件类型检测**: 通过文件扩展名和语言 ID 识别 Vue 文件
2. **Script 提取**: 使用 `PsiTreeUtil.findChildrenOfType()` 查找 `<script>` 标签
3. **内容解析**: 提取 script 标签中的 JSFile 对象
4. **复用现有逻辑**: 将提取的 JSFile 传递给现有的 TypeScript/JavaScript 分析器

### 兜底策略

- 如果 Vue 文件中没有 script 标签，返回适当的错误信息
- 如果 PSI 解析失败，记录警告日志但不中断分析流程
- 支持多种 Vue 语言 ID 变体（"Vue", "VueJS"）

## 使用方式

### 支持的文件类型

插件现在支持以下文件类型：
- `.ts` (TypeScript)
- `.tsx` (TypeScript JSX)
- `.js` (JavaScript)
- `.jsx` (JavaScript JSX)
- `.vue` (Vue.js 单文件组件) **[新增]**

### 分析功能

对于 Vue 文件，插件能够：
1. **导入分析**: 解析 script 标签中的 import/require 语句
2. **调用关系分析**: 分析函数调用和方法调用
3. **结构分析**: 分析类、函数、变量等代码结构
4. **统计信息**: 提供代码行数、复杂度等统计

### Vue 特有功能

- **Composition API 检测**: 识别 `<script setup>` 语法
- **TypeScript 支持**: 支持 `<script lang="ts">` 语法
- **组件导入**: 正确解析 `.vue` 文件的导入路径

## 测试建议

在 IDE 中测试以下场景：

1. **基本 Vue 文件**: 包含 template、script、style 的标准 Vue 文件
2. **TypeScript Vue**: 使用 `<script lang="ts">` 的 Vue 文件
3. **Composition API**: 使用 `<script setup>` 的 Vue 文件
4. **复杂导入**: 包含多种导入类型的 Vue 文件
5. **嵌套组件**: 导入其他 Vue 组件的文件

## 注意事项

1. 插件依赖 IntelliJ IDEA 的 Vue.js 插件来提供基础的 Vue 文件 PSI 支持
2. 目前主要关注 script 标签内容的分析，template 和 style 部分暂不处理
3. 需要确保项目中安装了适当的 Vue.js 支持插件

## 后续扩展

可以考虑的功能扩展：
1. Template 语法分析
2. Vue 指令识别
3. Props 和 Emit 分析
4. Vue Router 路由分析
5. Vuex/Pinia 状态管理分析
