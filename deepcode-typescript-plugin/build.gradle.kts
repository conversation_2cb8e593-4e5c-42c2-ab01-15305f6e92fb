plugins {
    id("java")
    id("org.jetbrains.intellij.platform") version "2.6.0"
}

group = "com.sankuai.deepcode"
version = "1.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenCentral()
    intellijPlatform {
        defaultRepositories()
    }
}

dependencies {
    implementation(project(":shared-core"))
    implementation(project(":shared-ui"))
    
    intellijPlatform {
        webstorm("2024.1.4") // 🚀 直接使用WebStorm
        bundledPlugins("JavaScript") // WebStorm内置的JavaScript支持
        
        // 插件验证和测试依赖
        pluginVerifier()
        zipSigner()
    }
}

intellijPlatform {
    pluginConfiguration {
        id.set("com.sankuai.deepcode.astplugin.typescript")
        name.set("DeepCode AST Analyzer for TypeScript")
        vendor {
            name.set("Shanghai Sankuai Technology Co., Ltd.")
            email.set("<EMAIL>")
        }
        description = """
            AST分析器 - TypeScript/JavaScript语言支持

            专门为WebStorm设计的TypeScript/JavaScript代码分析插件。
            支持TypeScript/JavaScript代码的AST解析、结构分析和调用关系分析。

            功能特性：
            - TypeScript/JavaScript 类、函数、方法结构解析
            - 接口和枚举分析（TypeScript特有）
            - React组件识别和分析
            - 函数调用关系分析
            - ES6模块导入语句分析
            - CommonJS require语句分析
            - 代码统计信息
            - 可视化AST结果展示
            
            支持的文件类型：
            - .ts (TypeScript)
            - .tsx (TypeScript JSX)
            - .js (JavaScript)
            - .jsx (JavaScript JSX)
            - .vue (Vue.js 单文件组件)
        """.trimIndent()
        
        ideaVersion {
            sinceBuild.set("241")
            untilBuild.set("252.*")
        }
    }
    
    pluginVerification {
        ides {
            recommended()
        }
    }
}

tasks {
    runIde {
        maxHeapSize = "2g"
        
        jvmArgs = listOf(
            "-Xms512m",
            "-Xmx2g",
            "-XX:ReservedCodeCacheSize=512m",
            "-XX:+UseConcMarkSweepGC",
            "-XX:SoftRefLRUPolicyMSPerMB=50",
            "-ea",
            "-XX:CICompilerCount=2",
            "-Dsun.io.useCanonPrefixCache=false",
            "-Djdk.http.auth.tunneling.disabledSchemes=\"\"",
            "-XX:+HeapDumpOnOutOfMemoryError",
            "-XX:-OmitStackTraceInFastThrow",
            "-Dide.show.tips.on.startup.default.value=false",
            "-Didea.ProcessCanceledException=disabled",
            // 简化日志配置 - 强制启用所有输出
            "-Didea.log.level=DEBUG",
            "-Dlog.level=DEBUG",
            "-Didea.log.debug.categories=#com.sankuai",
            // 强制所有输出到控制台
            "-Dcom.intellij.openapi.diagnostic.Logger.level=DEBUG"
        )
    }
    
    compileJava {
        options.compilerArgs.addAll(listOf("-Xlint:deprecation", "-Xlint:unchecked"))
    }
}

