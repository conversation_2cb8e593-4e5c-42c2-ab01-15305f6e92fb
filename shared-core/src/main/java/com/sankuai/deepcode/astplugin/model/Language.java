package com.sankuai.deepcode.astplugin.model;

/**
 * 支持的编程语言枚举
 * <AUTHOR>
 */
public enum Language {
    JAVA("java", "Java", ".java"),
    PYTHON("python", "Python", ".py"),
    JAVASCRIPT("javascript", "JavaScript", ".js"),
    TYPESCRIPT("typescript", "TypeScript", ".ts"),
    JSX("jsx", "JSX", ".jsx"),
    TSX("tsx", "TSX", ".tsx"),
    VUE("vue", "Vue", ".vue"),
    KOTLIN("kotlin", "Kotlin", ".kt"),
    SCALA("scala", "Scala", ".scala"),
    GO("go", "Go", ".go"),
    RUST("rust", "Rust", ".rs"),
    CPP("cpp", "C++", ".cpp"),
    C("c", "C", ".c"),
    CSHARP("csharp", "C#", ".cs"),
    PHP("php", "PHP", ".php"),
    RUBY("ruby", "Ruby", ".rb"),
    SWIFT("swift", "Swift", ".swift"),
    UNKNOWN("unknown", "Unknown", "");

    private final String code;
    private final String displayName;
    private final String fileExtension;

    Language(String code, String displayName, String fileExtension) {
        this.code = code;
        this.displayName = displayName;
        this.fileExtension = fileExtension;
    }

    public String getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getFileExtension() {
        return fileExtension;
    }

    /**
     * 根据代码获取语言枚举
     */
    public static Language fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        for (Language language : values()) {
            if (language.code.equalsIgnoreCase(code.trim())) {
                return language;
            }
        }
        return UNKNOWN;
    }

    /**
     * 根据文件扩展名获取语言枚举
     */
    public static Language fromFileExtension(String extension) {
        if (extension == null || extension.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        String normalizedExtension = extension.trim().toLowerCase();
        if (!normalizedExtension.startsWith(".")) {
            normalizedExtension = "." + normalizedExtension;
        }
        
        for (Language language : values()) {
            if (language.fileExtension.equalsIgnoreCase(normalizedExtension)) {
                return language;
            }
        }
        return UNKNOWN;
    }

    /**
     * 根据文件名获取语言枚举
     */
    public static Language fromFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            String extension = fileName.substring(lastDotIndex);
            return fromFileExtension(extension);
        }
        
        return UNKNOWN;
    }

    @Override
    public String toString() {
        return displayName;
    }
}